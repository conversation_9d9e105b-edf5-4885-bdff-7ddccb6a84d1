

<?php $__env->startSection("wrapper"); ?>
<div class="pagination-list d-flex w-100">
    <a>/ dashboard / contact / List</a>
</div>
<div class="content-section-box">





    <div class="datatable_parent">

        <table id="contact_category_listing" class="data_table display" style="width:100%">
            <thead>
                <tr>
                    <th style="min-width: 40px;"></th>

                    <th style="min-width: 120px;">Name</th>
                    <th style="min-width: 120px;">Email</th>
                    <th style="min-width: 330px;">Message</th>
                    <th style="min-width: 16px;">Action</th>
                </tr>
            </thead>
            <tbody>
                <?php $__currentLoopData = $contactRecords; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $contactRecords): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr class="category_row">
                    <td></td>
                    <td><?php echo e($contactRecords->name); ?></td>
                    <td><?php echo e($contactRecords->email); ?></td>
                    <td><?php echo e($contactRecords->message); ?></td>
                    <td>
                        <div class="form_action_box">
                            <button class="toggle-button">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="action_btn_group actions gap-3" style="display: none;">
                                <button data-id="<?php echo e($contactRecords->id); ?>" type="button"
                                    class="delete_btn delete_contact_category"><i class="fas fa-trash-alt"></i>
                                    Delete</button>

                                <button class="payment_btn reply_btn" tabindex="0" type="button"
                                    data-bs-toggle="offcanvas" data-bs-target="#offcanvasReply<?php echo e($contactRecords->id); ?>"
                                    data-form-id="reply_<?php echo e($contactRecords->id); ?>" data-id="<?php echo e($contactRecords->id); ?>">
                                    <i class="fas fa-comment-dots"></i> Reply
                                </button>

                                <button class="detail_btn reply_btn" tabindex="0" type="button"
                                    data-bs-toggle="offcanvas" data-bs-target="#offcanvasDetail<?php echo e($contactRecords->id); ?>">
                                    <i class="fas fa-info"></i> Detail
                                </button>
                            </div>
                        </div>
                        <?php echo $__env->make("dashboard.contact.reply", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        <?php echo $__env->make("dashboard.contact.detail", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                    </td>
                </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tbody>
        </table>
    </div>

</div>
<?php echo $__env->make("dashboard.contact.delete", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>


<?php $__env->stopSection(); ?>

<?php echo $__env->make("dashboard.include.layout", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\clients project\yared\travelafrica\resources\views/dashboard/contact/contactRecords.blade.php ENDPATH**/ ?>