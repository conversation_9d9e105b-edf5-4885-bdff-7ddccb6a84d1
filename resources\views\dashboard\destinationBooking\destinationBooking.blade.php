@extends("dashboard.include.layout")


@section("wrapper")
<div class="pagination-list d-flex w-100">
    <a> / destination Booking / list</a>
</div>
<div class="content-section-box">


    @php($user = auth()->user())


    <div class="datatable_parent">

        <table id="destination_listing" class="data_table display" style="width:100%">
            <thead>
                <tr>
                    <th style="min-width: 40px;"></th>
                    <th style="min-width: 250px;">Status</th>
                    <th style="min-width: 100px;">Name</th>
                    <th style="min-width: 100px;">Type</th>
                    <th style="min-width: 200px;">Destination Title</th>
                    <th style="min-width: 100px;">Phone</th>
                    <th style="min-width: 120px;">Date</th>
                    <th style="min-width: 120px;">Ticket</th>
                    <th style="min-width: 100px;">Adults</th>
                    <th style="min-width: 100px;">Childrens </th>
                    <th style="min-width: 100px;">Extras </th>
                    <th style="min-width: 100px;">Extras Price </th>
                    <th style="min-width: 100px;">Seperate Rooms </th>
                    <th style="min-width: 100px;">Destination Price </th>
                    <th style="min-width: 100px;">Destination Total Price </th>
                    <th style="min-width: 100px;">Total Pay Able </th>
                    <th style="min-width: 220px;">Message</th>
                    <th style="min-width: 16px;">Action</th>
                </tr>
            </thead>
            <tbody>
                @if ($BookingsData)
                @foreach ($BookingsData as $destinationBookingList)
                <?php
                $totalValue = 0;

                if (!empty($destinationBookingList->extras)) {
                    $extraTotalValue = 0;
                    foreach (explode(',', $destinationBookingList->extras) as $extras) {
                        $parts = explode('-', $extras);
                        $value = intval($parts[1]); // Convert the value to an integer
                        $extraTotalValue += $value; // Add the value to the total
                    }
                    //   $final_price_plus_extras = $extraTotalValue + $destinationBookingList->destination->final_price;
                } else {
                    $extraTotalValue = 0;
                    //  $final_price_plus_extras = $destinationBookingList->destination->final_price;
                }

                // if($destinationBookingList->tickets == 1){
                //   $destinationPrice=$destinationBookingList->destination->final_price;
                //   $destinationCountPrice=$destinationBookingList->destination->two_final_price * $destinationBookingList->tickets;
                // }
                // elseif($destinationBookingList->tickets == 2){
                //   $destinationPrice=$destinationBookingList->destination->two_final_price;
                //   $destinationCountPrice=$destinationBookingList->destination->two_final_price * $destinationBookingList->tickets;
                // }
                // elseif($destinationBookingList->tickets == 3 || $destinationBookingList->tickets == 4 ){
                //    $destinationPrice=$destinationBookingList->destination->three_to_four_final_price;
                //    $destinationCountPrice=$destinationBookingList->destination->two_final_price * $destinationBookingList->tickets;
                // }
                // elseif($destinationBookingList->tickets == 5 || $destinationBookingList->tickets == 6 ){
                //      $destinationPrice=$destinationBookingList->destination->five_to_six_final_price;
                //      $destinationCountPrice=$destinationBookingList->destination->two_final_price * $destinationBookingList->tickets;
                // }
                // elseif($destinationBookingList->tickets > 6 ){
                //    $destinationPrice=$destinationBookingList->destination->six_plus_final_price;
                //    $destinationCountPrice=$destinationBookingList->destination->two_final_price * $destinationBookingList->tickets;
                // }
                $destinationCountPrice = $destinationBookingList->single_price * $destinationBookingList->tickets;
                $roomAdonPrice = $destinationBookingList->seperate_room * $destinationBookingList->destination->single_room_price;

                $payAbleTotalPrice = $extraTotalValue + $destinationCountPrice + $roomAdonPrice;

                ?>
                <tr class="booking_row">
                    <td></td>

                    <td>
                        @if ($destinationBookingList->status === 'cancel')
                        <div class="payment_status_parent d-grid gap-3">
                            <p class=" p-2" style="background-color: #dc3545;">Cancelled
                            </p>
                        </div>
                        @elseif ($destinationBookingList->status === 'advancePaymentPending')
                        <div class="payment_status_parent d-grid gap-3">
                            <p class=" p-2" style=" background-color: #17a2b8;">Advance Payment
                                Pending
                            </p>
                        </div>
                        @elseif ($destinationBookingList->status === 'advancePaymentSuccess')
                        <div class="payment_status_parent d-grid gap-3">
                            <p class=" p-2" style="background-color: #28a745;">Advance Payment
                                Success
                            </p>

                            <button style=" background-color: #17a2b8;"
                                class="action_btn booking_remaining_payment_link  p-2" tabindex="0" type="button"
                                data-bs-toggle="offcanvas"
                                data-bs-target="#offcanvasRemainingPayment{{$destinationBookingList->id}}"
                                data-form-id="remaining_payment_{{$destinationBookingList->id}}"
                                data-id="{{$destinationBookingList->id}}">
                                Send Remaining Payment Link</button>
                        </div>
                        @elseif ($destinationBookingList->status === 'remainingPaymentPending')

                        <div class="payment_status_parent d-grid gap-3">
                            <p class=" p-2" style="background-color: #28a745;">Advance Payment
                                Success
                            </p>
                            <button style=" background-color: #17a2b8;"
                                class="action_btn booking_remaining_payment_link  p-2" tabindex="0" type="button"
                                data-bs-toggle="offcanvas"
                                data-bs-target="#offcanvasRemainingPayment{{$destinationBookingList->id}}"
                                data-form-id="remaining_payment_{{$destinationBookingList->id}}"
                                data-id="{{$destinationBookingList->id}}">
                                Send Remaining Payment Link</button>
                            <p class=" p-2" style="background-color:#ffc107; color:#000;">Remaining Payment
                                Pending
                            </p>
                        </div>

                        @elseif ($destinationBookingList->status === 'remainingPaymentSuccess')

                        <div class="payment_status_parent d-grid gap-3">
                            <p class=" p-2" style="background-color: #28a745;">All Payment Completed
                            </p>
                        </div>
                        @elseif ($destinationBookingList->status === 'completePaymentPending')

                        <div class="payment_status_parent d-grid gap-3">
                            <p class=" p-2" style="background-color: #17a2b8;">complete payment
                                pending
                            </p>
                        </div>

                        @elseif ($destinationBookingList->status === 'completePaymentSuccess')

                        <div class="payment_status_parent d-grid gap-3">
                            <p class=" p-2" style="background-color: #28a745;">complete payment
                                Done
                            </p>
                        </div>


                        @else
                        <div class="payment_status_parent d-grid gap-3">
                            <p class=" p-2" style="background-color:#ffc107; color:#000;">No Payment
                            </p>
                        </div>

                        @endif
                    </td>


                    <td>{{ $destinationBookingList->bookedBy->name ? $destinationBookingList->bookedBy->name : 'Guest' }}</td>
                    <td>{{ $destinationBookingList->type ? $destinationBookingList->type :  'public'}}</td>
                    <td>{{ $destinationBookingList->destination->title }}</td>
                    <td>{{ $destinationBookingList->phone }}</td>
                    <td>{{ $destinationBookingList->date }}</td>
                    <td>{{ $destinationBookingList->tickets }}</td>
                    <td>{{ $destinationBookingList->adultes }}</td>
                    <td>{{ $destinationBookingList->children }}</td>
                    <td>
                        <div class="d-flex align-items-center flex-wrap">
                            @if(!empty($destinationBookingList->extras))
                            @foreach (explode(',', $destinationBookingList->extras) as $extras)
                            <p class=" m-2 p-2" style="background-color: var(--ternary-bg-color);">{{ $extras }}</p>
                            @endforeach
                            @else
                            <p class=" m-2 p-2" style="background-color: var(--ternary-bg-color);">No Extras</p>
                            @endif
                        </div>
                    </td>
                    <td>
                        <p>{{ $extraTotalValue }}</p>
                    </td>

                    <td>{{ $destinationBookingList->seperate_room ? $destinationBookingList->seperate_room : 0 }}<i class="fas fa-times" style="padding:0px 5px"></i>{{ $destinationBookingList->destination->single_room_price }} = {{$roomAdonPrice}}</td>

                    <td>{{ $destinationBookingList->single_price }}</td>

                    <td>{{ $destinationBookingList->tickets }}<i class="fas fa-times" style="padding:0px 5px"></i>{{ $destinationBookingList->single_price }} = {{$destinationBookingList->single_price * $destinationBookingList->tickets }}</td>
                    <td>{{ $payAbleTotalPrice }}</td>
                    <td>{{ $destinationBookingList->message }}</td>
                    <td>
                        <div class="form_action_box">
                            <button class="toggle-button">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="action_btn_group actions gap-3" style="display: none;">



                                <a href="{{ route('dashboard.destinationBooking.updateBooking', $destinationBookingList->id) }}"
                                    class="update_btn update_booking_detail"><i class="far fa-edit"></i>Update</a>

                                <button class="payment_btn booking_advance_payment_btn" tabindex="0" type="button"
                                    data-bs-toggle="offcanvas"
                                    data-bs-target="#offcanvasAdvancePayment{{$destinationBookingList->id}}"
                                    data-form-id="advance_payment_{{$destinationBookingList->id}}"
                                    data-id="{{$destinationBookingList->id}}">
                                    <i class="fas fa-money-check"></i> Advance Payment Link </button>
                                <button class="payment_btn booking_complete_payment_btn" tabindex="0" type="button"
                                    data-bs-toggle="offcanvas"
                                    data-bs-target="#offcanvasCompletePayment{{$destinationBookingList->id}}"
                                    data-form-id="complete_payment_{{$destinationBookingList->id}}"
                                    data-id="{{$destinationBookingList->id}}">
                                    <i class="fas fa-money-check"></i> Complete payment link </button>
                                <button data-id="{{ $destinationBookingList->id }}" type="button"
                                    class="delete_btn delete_Booking_List"><i
                                        class="fas fa-trash-alt"></i>Delete</button>
                            </div>
                        </div>
                        @include("dashboard.destinationBooking.advancePayment")
                        @include("dashboard.destinationBooking.remainingPayment")
                        @include("dashboard.destinationBooking.completePayment")


                    </td>
                </tr>
                @endforeach
                @endif
            </tbody>
        </table>
    </div>

</div>

@include("dashboard.destinationBooking.delete")

@endsection