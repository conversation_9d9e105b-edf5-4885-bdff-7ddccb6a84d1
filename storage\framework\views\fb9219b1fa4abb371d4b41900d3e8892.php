<div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasReply<?php echo e($contactRecords->id); ?>"
    aria-labelledby="offcanvasReply<?php echo e($contactRecords->id); ?>Label">
    <!-- Offcanvas Header -->
    <div class="offcanvas-header py-4">
        <h5 id="offcanvasReply<?php echo e($contactRecords->id); ?>Label" class="offcanvas-title">Reply</h5>
        <button type="button" class="btn-close bg-label-secondary text-reset" data-bs-dismiss="offcanvas"
            aria-label="Close"></button>
    </div>
    <!-- Offcanvas Body -->
    <div class="offcanvas-body border-top">
        <form class="pt-0" id="reply_<?php echo e($contactRecords->id); ?>">
            <?php echo csrf_field(); ?>
            <input type="hidden" name="id" value="<?php echo e($contactRecords->id); ?>" />
            <div class="d-grid gap-3 w-100">

                <div class="form_field_box d-grid gap-2 w-100">
                    <label for="message">Message</label>
                    <textarea class="custom_form_field" name="message" id="message" rows="10"></textarea>
                </div>

                <!-- Submit and reset -->
                <div class="d-grid gap-2 w-100">
                    <button type="submit" class="custom_btn_2">Submit
                        <div class="form_loader position-absolute top-0 w-100 h-100 d-none align-items-center justify-content-center"
                            style="background-color: var(--secondary-bg-color);">
                            <img class="loader" src="<?php echo e(asset('dashboard/img/loader.gif')); ?>" style="width:30px">
                        </div>
                    </button>

                    <button type="reset" class="custom_btn_3 w-100" data-bs-dismiss="offcanvas">Discard</button>
                </div>
            </div>
        </form>
    </div>
</div>
<?php /**PATH D:\clients project\yared\travelafrica\resources\views/dashboard/contact/reply.blade.php ENDPATH**/ ?>